{% extends "partials/base.html" %}
{% load static %}
{% block content %}
{% comment %} <style>
    .statistics {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-top: 4em;
    }


    .bar-chart,
    .pie-chart {
        background-color: white;
        height: 60vh;
        border: 1px solid grey;
        /*background-color: transparent;*/
        border-radius: 5px;
        /*backdrop-filter: blur(10px);*/
        padding: 10px;
        box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    }

    /* Media query for tablets and smaller screens */
    @media (max-width: 768px) {
        .statistics {
            grid-template-columns: 1fr;
            /* Stacks the charts vertically */
        }

        .bar-chart,
        .pie-chart {
            height: 70vh;
            width: 75vw;
        }
    }

    /* Media query for mobile screens */
    @media (max-width: 400px) {

        .bar-chart,
        .pie-chart {
            height: 70vh;
            width: 43vh;
            padding: 5px;
        }

        h2 {
            font-size: 1.5em;
        }
    }
</style> {% endcomment %}

<div
    style="background-image: url({% static 'images/pharmacy2.jpg' %}); height: 100vh; background-size: cover; background-position: center;">


    
    
    {% if user.is_superuser %}
    {% for message in messages %}
    <div style="text-align: center;" class="alert alert-{{message.tags}}">{{message}}</div>
    {% endfor %}

    <!-- Quick Access Section -->
    <div class="container-fluid mb-4" style="background: rgba(255, 255, 255, 0.95); border-radius: 10px; padding: 20px; margin: 20px;">
        <h4 class="text-center mb-4" style="color: #333;">
            <i class="fas fa-tachometer-alt me-2"></i>Quick Access Dashboard
        </h4>
        <div class="row">
            <!-- Dispensing Management Card -->
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    Dispensing Management</div>
                                <div class="h6 mb-0 font-weight-bold text-gray-800">Track & Analyze</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-pills fa-2x text-gray-300"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'store:dispensing_log' %}" class="btn btn-primary btn-sm me-2">
                                <i class="fas fa-clipboard-list"></i> View Log
                            </a>
                            {% if user.is_superuser or user.is_staff or user.profile.user_type == 'Admin' or user.profile.user_type == 'Manager' %}
                            <a href="{% url 'store:user_dispensing_summary' %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-chart-bar"></i> All Users
                            </a>
                            {% else %}
                            <a href="{% url 'store:my_dispensing_details' %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-user-chart"></i> My Details
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Store Operations Card -->
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    Store Operations</div>
                                <div class="h6 mb-0 font-weight-bold text-gray-800">Retail & Wholesale</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-store fa-2x text-gray-300"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'store:store' %}" class="btn btn-success btn-sm me-2">
                                <i class="fas fa-shopping-cart"></i> Retail
                            </a>
                            <a href="{% url 'wholesale:wholesales' %}" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-warehouse"></i> Wholesale
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reports Card -->
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    Reports & Analytics</div>
                                <div class="h6 mb-0 font-weight-bold text-gray-800">Business Insights</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'store:daily_sales' %}" class="btn btn-info btn-sm me-2">
                                <i class="fas fa-calendar-day"></i> Daily Sales
                            </a>
                            <a href="{% url 'store:monthly_sales' %}" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-calendar-alt"></i> Monthly
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="statistics">
        <div class="bar-chart">
            <canvas id="myAreaChart"></canvas> <!-- Changed ID to myAreaChart -->
        </div>
        <div class="pie-chart">
            <canvas id="myPieChart"></canvas> <!-- Changed ID to myPieChart -->
        </div>
    </div>
    {% endif %}
</div>

<!-- Include Chart.js and HTMX -->
<script src="{% static 'js/chart.min.js' %}"></script>
<script src="https://unpkg.com/htmx.org@1.9.0"></script>
<!-- Page level custom scripts -->
<script src="{% static 'js/demo/chart-area-demo.js' %}"></script>
<script src="{% static 'js/demo/chart-pie-demo.js' %}"></script>
<!-- <script>
    // Function to render daily sales chart
    function renderDailySalesChart(data) {
        const labels = data.map(item => new Date(item.day).toLocaleDateString());
        const sales = data.map(item => item.total_sales);

        const dailySalesData = {
            labels: labels,
            datasets: [{
                label: 'Daily Sales',
                data: sales,
                backgroundColor: 'rgba(54, 162, 235, 1)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        };

        const dailySalesConfig = {
            type: 'bar',
            data: dailySalesData,
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        };

        const dailySalesCtx = document.getElementById('dailySalesChart').getContext('2d');
        new Chart(dailySalesCtx, dailySalesConfig);
    }

    // Function to render monthly sales chart
    function renderMonthlySalesChart(data) {
        const labels = data.map(item => new Date(item.month).toLocaleString('default', { month: 'long' }));
        const sales = data.map(item => item.total_sales);

        const monthlySalesData = {
            labels: labels,
            datasets: [{
                label: 'Monthly Sales',
                data: sales,
                backgroundColor: [
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(153, 102, 255, 1)',
                    'rgba(255, 159, 64, 1)',
                    'rgba(201, 203, 207, 1)',
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(153, 102, 255, 1)'
                ],
                hoverOffset: 4
            }]
        };

        const monthlySalesConfig = {
            type: 'pie',
            data: monthlySalesData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                }
            }
        };

        const monthlySalesCtx = document.getElementById('monthlySalesChart').getContext('2d');
        new Chart(monthlySalesCtx, monthlySalesConfig);
    } -->

<!-- // Fetch and render sales data using HTMX
    document.addEventListener('DOMContentLoaded', function () {
        htmx.ajax('GET', ', '#dailySalesChart', function (response) {
            renderDailySalesChart(response.daily_sales);
        });

        htmx.ajax('GET', '', '#monthlySalesChart', function (response) {
            renderMonthlySalesChart(response.monthly_sales);
        });
    });
</script> -->
{% endblock %}